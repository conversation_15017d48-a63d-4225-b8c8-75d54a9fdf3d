// Simple test component to debug the blank page issue
import React from "react";

export default function App() {
  return (
    <div style={{
      padding: "20px",
      backgroundColor: "#f0f0f0",
      minHeight: "100vh",
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ color: "#333" }}>🎉 React App is Working!</h1>
      <p style={{ color: "#666" }}>If you can see this, <PERSON>act is rendering correctly.</p>
      <div style={{
        backgroundColor: "white",
        padding: "20px",
        borderRadius: "8px",
        marginTop: "20px",
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
      }}>
        <h2>Test Dashboard</h2>
        <p>This is a temporary test component to verify <PERSON>act is working.</p>
        <button
          style={{
            backgroundColor: "#007bff",
            color: "white",
            border: "none",
            padding: "10px 20px",
            borderRadius: "4px",
            cursor: "pointer"
          }}
          onClick={() => alert("But<PERSON> clicked! React is working.")}
        >
          Test Button
        </button>
      </div>
    </div>
  );
}

{"name": "soft-ui-dashboard-react", "version": "4.0.1", "author": "Creative Tim", "license": "SEE LICENSE IN <https://www.creative-tim.com/license>", "description": "ReactJS version of Soft UI Dashboard by Creative Tim", "bugs": {"url": "https://github.com/creativetimofficial/soft-ui-dashboard-react/issues"}, "repository": {"type": "git", "url": "git+https://github.com/creativetimofficial/soft-ui-dashboard-react.git"}, "dependencies": {"@emotion/cache": "11.11.0", "@emotion/react": "11.11.0", "@emotion/styled": "11.11.0", "@mui/icons-material": "5.11.16", "@mui/material": "5.13.0", "@mui/styled-engine": "5.12.3", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "14.0.0", "@testing-library/user-event": "14.4.3", "ajv": "^8.17.1", "chart.js": "3.9.1", "chroma-js": "2.4.2", "prop-types": "15.8.1", "react": ">=16.8.0", "react-chartjs-2": "3.0.5", "react-countup": "6.4.2", "react-dom": ">=16.8.0", "react-flatpickr": "3.10.13", "react-router-dom": "6.11.1", "react-scripts": "5.0.1", "stylis": "4.2.0", "stylis-plugin-rtl": "2.1.1", "uuid": "9.0.0", "web-vitals": "3.3.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "install:clean": "rm -rf node_modules/ && rm -rf package-lock.json && npm install && npm start", "install:peer-deps": "npm install --legacy-peer-deps"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "8.40.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "prettier": "2.8.8"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "overrides": {"svgo": "3.0.2"}}